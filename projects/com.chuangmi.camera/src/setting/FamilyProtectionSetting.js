import React from 'react';
import {
  ScrollView,
  View,
  BackHandler,
  FlatList,
  Platform,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions
} from 'react-native';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage} from '../../../../imilab-rn-sdk';
import {imiThemeManager} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import Switch from '../../../../imilab-design-ui/src/widgets/settingUI/Switch';
import {LetDevice} from '../../../../imilab-rn-sdk';
import {showToast} from '../../../../imilab-design-ui';

const MAX_ITEM_NUM = 8;
const viewHeight = (Dimensions.get('window').width - 48) * 9 / 16;
const TAG = "FamilyProtectionSetting";

export default class FamilyProtectionSetting extends React.Component {
  constructor(props, context) {
    super(props, context);
    this.state = {
      isLoading: false,
      listDatas: [],
      editMode: false,
      showBarTitle: false,
      isAllSelect: false
    };
    this.didFocusListener = this.props.navigation.addListener(
      'didFocus',
      () => {
        this.onResume();
      }
    );
    this.isPageForeGround = true;
    this.didBlurListener = this.props.navigation.addListener(
      'didBlur',
      () => {
        this.isPageForeGround = false;
      }
    );
    this.keys = [];
    this.LONG_TIME_KEY_PREFIX = "prop.s_chuangmi_clocks";
    this.selectCount = 0;
    this.deleteArray = [];
    this.deleteAlarmKeys = {};
    this.titleHeight = 38;
  }

  renderTitleBar() {
    let titleStr = this.state.editMode ? stringsTo('edit') : stringsTo('family_protection');

    // 构建右侧按钮
    let rightButtons = [];
    if (this.state.listDatas && this.state.listDatas.length > 0) {
      if (this.state.editMode) {
        // 编辑模式下显示全选/取消全选
        rightButtons.push({
          key: NavigationBar.ICON.CUSTOM,
          n_source: this.state.isAllSelect
            ? require('../../resources/images/selected_all_n_light.png')
            : require('../../resources/images/select_all_n_light.png'),
          p_source: this.state.isAllSelect
            ? require('../../resources/images/selected_all_p_light.png')
            : require('../../resources/images/select_all_p_light.png'),
          style: {width: 32, height: 32, resizeMode: 'contain'},
          onPress: () => {
            if (this.selectCount >= this.state.listDatas.length) {
              this.selectCount = 0;
              this.setState({
                listDatas: this.state.listDatas.map((item, _index) => {
                  return {...item, select: false};
                }),
                isAllSelect: false
              });
            } else {
              this.selectCount = this.state.listDatas.length;
              this.setState({
                listDatas: this.state.listDatas.map((item, _index) => {
                  return {...item, select: true};
                }),
                isAllSelect: true
              });
            }
          }
        });
      } else {
        // 正常模式下显示编辑按钮
        rightButtons.push({
          key: NavigationBar.ICON.CUSTOM,
          n_source: require('../../resources/images/houseKeepingV2/icon_angel_edit.png'),
          style: {width: 32, height: 32, resizeMode: 'contain'},
          onPress: () => {
            this.selectCount = 0;
            this.setState({editMode: true});
          }
        });
      }
    }

    return (
      <NavigationBar
        title={this.state.showBarTitle ? titleStr : " "}
        left={[
          {
            key: this.state.editMode ? NavigationBar.ICON.CUSTOM : NavigationBar.ICON.BACK,
            n_source: this.state.editMode ? require('../../resources/images/close.png') : undefined,
            onPress: () => {
              if (this.state.editMode) {
                this.state.listDatas.forEach((item) => item.select = false);
                this.selectCount = 0;
                this.setState({editMode: false, isAllSelect: false});
              } else {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              }
            }
          }
        ]}
        right={rightButtons}
      />
    );
  }

  componentDidMount() {
    if (Platform.OS === "android") {
      BackHandler.addEventListener("hardwareBackPress", this.onBackHandler);
    }
    this.loadData();
  }

  onItemLongClick(item) {
    item.select = true;
    this.selectCount = 1;
    this.setState({editMode: true, isAllSelect: this.state.listDatas.length === 1});
  }

  renderItemView(item, index) {
    return (
      <View style={{display: "flex", flexDirection: "column"}}>
        <View style={{display: "flex", flexDirection: "row", alignItems: "center", paddingRight: 20}}>
          <TouchableOpacity
            style={{display: "flex", flexDirection: "column", flexGrow: 2, padding: 20, width: "80%"}}
            onLongPress={() => this.onItemLongClick(item)}
            onPress={() => {
              if (this.state.editMode) {
                item.select = !item.select;
                item.select ? this.selectCount++ : this.selectCount--;
                this.setState({isAllSelect: this.state.listDatas.length === this.selectCount});
              } else {
                this.props.navigation.navigate('FamilyProtectionTimeSetting',
                  {
                    item: JSON.parse(JSON.stringify(item)),
                    callback: (data) => {
                      Object.assign(item, data);
                      this.onItemCheckChanged();
                      this.forceUpdate();
                    }
                  });
              }
            }}>
            <Text style={{fontSize: 16, color: '#000000', fontWeight: 'bold'}}>{item.name}</Text>
            <Text style={{
              fontSize: 13,
              color: 'rgba(0,0,0,0.6)',
              marginTop: 3
            }}>{this.getDurationText(item.start, item.end)} | {this.getRepeatString(item.repeat)}</Text>
          </TouchableOpacity>

          {!this.state.editMode && (
            <View style={styles.switchContainer}>
              <Switch
                value={item.enable}
                onValueChange={(checked) => {
                  console.log('开关状态变更:', item.name, checked);
                  item.enable = checked;
                  this.onItemCheckChanged();
                  this.forceUpdate(); // 强制更新UI
                }}
              />
            </View>
          )}

          {this.state.editMode && (
            <View style={styles.checkboxContainer}>
              <TouchableOpacity
                style={[styles.checkbox, item.select && styles.checkboxSelected]}
                onPress={() => {
                  item.select = !item.select;
                  item.select ? this.selectCount++ : this.selectCount--;
                  this.setState({isAllSelect: this.state.listDatas.length === this.selectCount});
                }}
              >
                {item.select && (
                  <Text style={styles.checkboxText}>✓</Text>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }

  getDurationText(start, end) {
    let text = `${start}-${end}`;
    let startValue = parseInt(start.split(":")[0]) * 60 + parseInt(start.split(":")[1]);
    let endValue = parseInt(end.split(":")[0]) * 60 + parseInt(end.split(":")[1]);
    if (startValue > endValue) {
      text = `${start}-${stringsTo('setting_monitor_next_day')}${end}`;
    }
    return text;
  }

  getRepeatString(repeat) {
    if (repeat === 0) {
      return stringsTo('plug_timer_onetime');
    } else if (repeat === 0b01111111) {
      return stringsTo('plug_timer_everyday');
    } else {
      const weekDays = [
        stringsTo('sunday1'),
        stringsTo('monday1'),
        stringsTo('tuesday1'),
        stringsTo('wednesday1'),
        stringsTo('thursday1'),
        stringsTo('friday1'),
        stringsTo('saturday1')
      ];
      let selectedDays = [];
      let flag = 0b00000001;
      for (let i = 0; i < 7; i++) {
        if ((repeat & flag) !== 0) {
          selectedDays.push(weekDays[i]);
        }
        flag = flag << 1;
      }
      return selectedDays.join(' ');
    }
  }

  scrollViewScroll = (event) => {
    const y = event.nativeEvent.contentOffset.y;
    let flag = y > this.titleHeight;
    if (this.showTitle == flag) {
      return;
    }
    if (flag) {
      this.showTitle = true;
      this.setState({showBarTitle: true});
    } else {
      this.showTitle = false;
      this.setState({showBarTitle: false});
    }
  };

  render() {
    let titleStr = this.state.editMode ? stringsTo('edit') : stringsTo('family_protection');
    return (<View style={styles.container}>
      {this.renderTitleBar()}
      <ScrollView scrollEventThrottle={16} onScroll={this.scrollViewScroll}
                  style={{width: "100%", backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF'}}
                  contentContainerStyle={{flexGrow: 1}}>

        <View style={{backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF'}}>
          <View style={{flexDirection: "row", flexWrap: "wrap"}} key={0}
                onLayout={({nativeEvent: {layout: {height}}}) => {
                  this.titleHeight = height - 28;
                }}>
            <Text style={styles.titleText}>
              {titleStr}
            </Text>
          </View>
          {
            !this.state.editMode ?
              <View style={{alignItems: "center", marginHorizontal: 24, marginTop: 0}}>
                <Image style={{width: "100%", height: viewHeight, borderRadius: 9}}
                       source={require("../../resources/images/ai_pic_area.webp")}
                       resizeMode="cover" />
              </View> : null
          }

          {
            !this.state.editMode ?
              <Text
                style={[styles.desc_subtitle, {marginTop: 20}]}>{stringsTo('family_protection_desc')}</Text> : null
          }

          {
            (!this.state.editMode && this.state.listDatas.length > 0) ?
              <View style={[styles.whiteblank, {marginTop: 38}]}/> : null
          }

          <FlatList
            style={{width: "100%", marginBottom: 100}}
            data={this.state.listDatas}
            renderItem={(data) => this.renderItemView(data.item, data.index)}
            contentContainerStyle={[{flexGrow: 1, paddingHorizontal: 12}]}
            refreshing={this.state.isLoading}>
          </FlatList>
        </View>
      </ScrollView>
      {this.state.editMode ?
        <View style={styles.editModeBottomView}>
          <TouchableOpacity
            style={styles.deleteButtonContainer}
            onPress={() => {
              this.onDeleteSelected();
            }}>
            <Image
              style={{width: 25, height: 25}}
              source={require("../../resources/images/icon_delete.png")}
            />
            <Text style={{color: "#000000", fontSize: 11}}>
              {stringsTo("delete_files")}
            </Text>
          </TouchableOpacity>
        </View>
        :
        <TouchableOpacity
          style={{position: 'absolute', right: 20, bottom: 30}}
          disabled={this.state.listDatas.length >= MAX_ITEM_NUM}
          onPress={() => {
            this.onAddItem();
          }}>
          <View style={styles.addButton}>
            <Text style={styles.addButtonText}>+</Text>
          </View>
        </TouchableOpacity>
      }
    </View>);
  }

  onDeleteSelected() {
    this.deleteArray = [];
    this.deleteAlarmKeys = {};
    let needDelete = false;
    this.state.listDatas.forEach((item) => {
      if (item.select == false) {
        if (item.name) {
          this.deleteAlarmKeys[`${this.LONG_TIME_KEY_PREFIX}${item.clock_idx}`] = item.name;
        }
        this.deleteArray.push({
          enable: item.enable,
          clock_idx: item.clock_idx,
          start: item.start,
          end: item.end,
          repeat: item.repeat,
          name: item.name
        });
      } else {
        needDelete = true;
      }
    });
    if (needDelete == false) {
      console.log(`onDeleteItem no item to delete`);
      showToast(stringsTo('idm_empty_tv_device_tips'));
      return;
    }
    this.onDeleteItem();
  }

  onDeleteItem() {
    let arrayStr = JSON.stringify({idx: 2, clock: this.deleteArray});
    console.log(`onDeleteItem ${arrayStr}`);
    this.putLongTimeAlarmList(arrayStr, this.deleteAlarmKeys).then(() => {
      this.setState({editMode: false});
      this.selectCount = 0;
      this.loadData();
    }).catch((err) => {
      showToast(stringsTo("c_set_fail"));
    });
  }

  onAddItem() {
    this.props.navigation.navigate('FamilyProtectionTimeSetting', {
      existList: this.state.listDatas,
      callback: (data) => {
        console.log(`alarmDataResultListener=${JSON.stringify(data)}`);
        let array = [];
        let alarmKeys = {};
        let key = 0;
        this.state.listDatas.forEach((item, index) => {
          if (key == this.keys[index]) {
            key += 1;
          }
          if (item.name) {
            alarmKeys[`${this.LONG_TIME_KEY_PREFIX}${item.clock_idx}`] = item.name;
          }
          array.push({
            enable: item.enable,
            clock_idx: item.clock_idx,
            start: item.start,
            end: item.end,
            repeat: item.repeat,
            name: item.name
          });
        });

        if (data.name) {
          alarmKeys[`${this.LONG_TIME_KEY_PREFIX}${key}`] = data.name;
        }
        array.push({
          enable: true,
          clock_idx: key,
          start: data.start,
          end: data.end,
          repeat: data.repeat,
          name: data.name
        });
        let arrayStr = JSON.stringify({idx: 2, clock: array});
        console.log(`onAddItem ${arrayStr}`);

        // ========== 临时数据源 - 添加新时段 ==========
        // TODO: 临时模拟添加功能，后期需要删除并接入真实接口
        const newItem = {
          enable: true,
          clock_idx: this.state.listDatas.length,
          start: data.start,
          end: data.end,
          repeat: data.repeat,
          name: data.name,
          select: false
        };

        const newListDatas = [...this.state.listDatas, newItem];
        this.setState({
          listDatas: newListDatas
        });

        showToast(stringsTo("c_set_success"));
        console.log('=== 临时数据源 === 添加新时段:', newItem);
        // ========== 临时数据源结束 ==========

        /* TODO: 后期接入真实接口
        this.putLongTimeAlarmList(arrayStr, alarmKeys).then((res) => {
          showToast(stringsTo("c_set_success"));
          this.loadData();
        }).catch((err) => {
          showToast(stringsTo("c_set_fail"));
        });
        */
      }
    });
  }

  onResume() {

  }

  onItemCheckChanged() {
    let array = [];
    let alarmKeys = {};
    this.state.listDatas.forEach((item) => {
      if (item.name) {
        alarmKeys[`${this.LONG_TIME_KEY_PREFIX}${item.clock_idx}`] = item.name;
      }
      array.push({
        enable: item.enable,
        clock_idx: item.clock_idx,
        start: item.start,
        end: item.end,
        repeat: item.repeat,
        name: item.name
      });
    });
    let data = JSON.stringify({idx: 2, clock: array});
    console.log(`onItemCheckChanged ${data}`);
    // ========== 临时数据源 - 开关状态变更 ==========
    // TODO: 临时模拟开关功能，后期需要删除并接入真实接口
    console.log('=== 临时数据源 === 开关状态变更:', data);
    // 直接更新本地状态，不调用接口
    this.forceUpdate();
    // ========== 临时数据源结束 ==========

    /* TODO: 后期接入真实接口
    this.putLongTimeAlarmList(data, alarmKeys).then((res) => {
    }).catch((err) => {
      showToast(stringsTo("c_set_fail"));
    });
    */
  }

  setAlarmKey(alarmKeys) {
    let alarmKeysStr = JSON.stringify(alarmKeys);
    console.log(alarmKeysStr);
    if (alarmKeysStr == "{}") {
      console.log("alarmKeys is {}");
      return;
    }
    // 使用物模型方式设置告警键值
    LetDevice.setPropertyCloud(JSON.stringify({FamilyProtectionAlarmKeys: alarmKeys})).then((res) => {
      console.log(JSON.stringify(res));
    }).catch((err) => {
      console.log(JSON.stringify(err));
    });
  }

  putLongTimeAlarmList(data, alarmKeys) {
    return new Promise((resolve, reject) => {
      // 使用物模型方式设置数据
      LetDevice.setPropertyCloud(JSON.stringify({FamilyCareTimePeriod: data})).then((res) => {
        this.setAlarmKey(alarmKeys);
        console.log(`putLongTimeAlarmList${JSON.stringify(res)}`);
        resolve(data);
      }).catch((err) => {
        console.log(`putLongTimeAlarmList err=${JSON.stringify(err)}`);
        this.loadData();
        reject(err);
      });
    });
  }

  loadData() {
    this.setState({
      isLoading: true
    });

    // ========== 临时数据源 - 初始化数据 ==========
    // TODO: 临时模拟数据加载，后期需要删除并接入真实接口
    console.log('=== 临时数据源 === 加载家人守护时段数据');

    // 模拟两条临时数据
    const tempData = [
      {
        enable: false,
        clock_idx: 0,
        start: "08:00",
        end: "12:00",
        repeat: 0b01111111, // 每天
        name: "上午守护时段",
        select: false
      },
      {
        enable: true,
        clock_idx: 1,
        start: "18:00",
        end: "22:00",
        repeat: 0b01111111, // 每天
        name: "晚上守护时段",
        select: false
      }
    ];

    this.keys = [0, 1];

    setTimeout(() => {
      this.setState({
        isLoading: false,
        listDatas: tempData
      });
      console.log('=== 临时数据源 === 加载完成:', tempData);
    }, 500); // 模拟网络延迟
    // ========== 临时数据源结束 ==========

    /* TODO: 后期接入真实接口
    // 使用物模型方式获取数据
    LetDevice.getPropertyCloud('FamilyCareTimePeriod').then((result) => {
      console.log("[[[[[[[[[", typeof (result), result);
      if (typeof (result) != "undefined" && result != "") {
        let valueObj = JSON.parse(result);
        let values = valueObj.clock;
        this.keys = [];
        values.forEach((item) => {
          item.select = false;
          this.keys.push(item.clock_idx);
        });
        this.keys.sort((a1, a2) => {
          return a1 - a2;
        });
        console.log(this.keys);
        this.setState({
          isLoading: false,
          listDatas: values
        });
      } else {
        this.productData();
      }
    }).catch((err) => {
      console.log("loadData error:", err);
      // 如果获取失败，初始化默认数据
      this.productData();
    });
    */
  }

  productData() {
    let data = JSON.stringify({
      clock: [
        {
          enable: false,
          clock_idx: 0,
          start: "07:00",
          end: "09:00",
          repeat: 0b01111111,
          name: stringsTo('detect_nobody_in_morning')
        },
        {
          enable: false,
          clock_idx: 1,
          start: "11:00",
          end: "13:00",
          repeat: 0b01111111,
          name: stringsTo('nobody_have_lunch')
        },
        {
          enable: false,
          clock_idx: 2,
          start: "06:00",
          end: "20:00",
          repeat: 0b01111111,
          name: stringsTo('detect_nobody_in_day')
        }
      ]
    });
    let alarmKeys = {
      "prop.s_chuangmi_clocks0": stringsTo('detect_nobody_in_morning'),
      "prop.s_chuangmi_clocks1": stringsTo('nobody_have_lunch'),
      "prop.s_chuangmi_clocks2": stringsTo('detect_nobody_in_day')
    };
    this.putLongTimeAlarmList(data, alarmKeys).then((res) => {
      this.loadData();
    });
  }

  componentWillUnmount() {
    if (Platform.OS === "android") {
      BackHandler.removeEventListener("hardwareBackPress", this.onBackHandler);
    }
  }

  onBackHandler = () => {
    if (this.state.editMode) {
      this.state.listDatas.forEach((item) => item.select = false);
      this.setState({editMode: false});
      this.selectCount = 0;
      return true;
    } else {
      return false;
    }
  };
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg || '#FFFFFF',
  },
  titleText: {
    fontSize: 30,
    color: "rgba(0, 0, 0, 0.80)",
    fontWeight: "300",
    position: "relative",
    marginLeft: 25,
    marginTop: 3,
    marginBottom: 23
  },
  desc_subtitle: {
    textAlign: 'left',
    color: "rgba(0, 0, 0, 0.6)",
    fontSize: 14,
    marginTop: 10,
    lineHeight: 21,
    marginHorizontal: 28
  },
  whiteblank: {
    height: 0.5,
    marginHorizontal: 24,
    backgroundColor: "#e5e5e5",
    marginBottom: 30,
    marginTop: 20
  },
  switchContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 10,
    width: 60,
  },
  checkboxContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingRight: 10,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 3,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  checkboxSelected: {
    backgroundColor: '#007AFF',
    borderColor: '#007AFF',
  },
  checkboxText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  editModeBottomView: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    width: "100%",
    position: 'absolute',
    bottom: 15
  },
  deleteButtonContainer: {
    flexDirection: "column",
    alignItems: "center"
  },
  addButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#4CAF50',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  addButtonText: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  }
});